// 全局变量
let chart = null;
let stagesContainer = null;
let addStageBtn = null;

// 情感指示器映射
const sentimentEmojis = {
    0: '😢', 10: '😢', 20: '😟', 30: '😕', 40: '😐',
    50: '😐', 60: '🙂', 70: '😊', 80: '😊', 90: '😄', 100: '😄'
};

// 获取情感指示器
function getSentimentEmoji(value) {
    const ranges = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
    for (let i = ranges.length - 1; i >= 0; i--) {
        if (value >= ranges[i]) {
            return sentimentEmojis[ranges[i]];
        }
    }
    return sentimentEmojis[0];
}

// 更新情感指示器
function updateSentimentIndicator(input) {
    const value = parseInt(input.value) || 0;
    const indicator = input.parentElement.querySelector('.sentiment-indicator');
    indicator.textContent = getSentimentEmoji(value);
}

// 更新心情曲线
function updateSentimentChart() {
    if (!chart) return;

    const stages = Array.from(document.querySelectorAll('.journey-stage[data-stage]'));
    const sentimentData = [];
    const stageLabels = [];

    stages.forEach(stage => {
        const input = stage.querySelector('.sentiment-input');
        const header = stage.querySelector('.stage-header');
        if (input && header) {
            sentimentData.push(parseInt(input.value) || 0);
            stageLabels.push(header.textContent.trim());
        }
    });

    chart.updateOptions({
        series: [{
            data: sentimentData
        }],
        xaxis: {
            categories: stageLabels
        }
    });
}

// 自定义确认对话框功能
function showCustomConfirm(onConfirm) {
    const overlay = document.getElementById('customConfirm');
    const confirmBtn = document.getElementById('confirmDelete');
    const cancelBtn = document.getElementById('confirmCancel');

    // 显示对话框
    overlay.classList.add('show');

    // 确认按钮事件
    const handleConfirm = () => {
        overlay.classList.remove('show');
        onConfirm();
        cleanup();
    };

    // 取消按钮事件
    const handleCancel = () => {
        overlay.classList.remove('show');
        cleanup();
    };

    // 点击遮罩层关闭
    const handleOverlayClick = (e) => {
        if (e.target === overlay) {
            overlay.classList.remove('show');
            cleanup();
        }
    };

    // ESC键关闭
    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            overlay.classList.remove('show');
            cleanup();
        }
    };

    // 清理事件监听器
    const cleanup = () => {
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
        overlay.removeEventListener('click', handleOverlayClick);
        document.removeEventListener('keydown', handleKeyDown);
    };

    // 添加事件监听器
    confirmBtn.addEventListener('click', handleConfirm);
    cancelBtn.addEventListener('click', handleCancel);
    overlay.addEventListener('click', handleOverlayClick);
    document.addEventListener('keydown', handleKeyDown);
}

document.addEventListener('DOMContentLoaded', () => {
    stagesContainer = document.querySelector('.journey-stages-container');
    addStageBtn = document.querySelector('.add-stage-button-container .add-btn');

    // 图表配置
    const chartOptions = {
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            },
            pan: {
                enabled: false
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            },
            selection: {
                enabled: false
            },
            events: {
                markerClick: function(event) {
                    // 阻止默认的选择行为
                    event.preventDefault();
                    return false;
                }
            }
        },
        series: [{
            name: '情感值',
            data: []
        }],
        xaxis: {
            categories: []
        },
        yaxis: {
            min: 0,
            max: 100,
            title: {
                text: '情感值'
            },
            labels: {
                formatter: function(value) {
                    if (value >= 80) return value + ' 😄';
                    if (value >= 60) return value + ' 😊';
                    if (value >= 40) return value + ' 😐';
                    if (value >= 20) return value + ' 😟';
                    return value + ' 😢';
                }
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        markers: {
            size: 8,
            colors: ['#0d6efd'],
            strokeColors: '#fff',
            strokeWidth: 2,
            discrete: [],
            hover: {
                size: 8,
                sizeOffset: 0
            }
        },
        states: {
            normal: {
                filter: {
                    type: 'none'
                }
            },
            hover: {
                filter: {
                    type: 'none'
                }
            },
            active: {
                filter: {
                    type: 'none'
                }
            }
        },
        selection: {
            enabled: false
        },
        grid: {
            borderColor: '#e9ecef',
        },
        tooltip: {
            custom: function({series, seriesIndex, dataPointIndex, w}) {
                const value = series[seriesIndex][dataPointIndex];
                const stage = w.globals.categoryLabels[dataPointIndex];

                // 获取对应阶段的想法和感受以及用户行为
                const stages = document.querySelectorAll('.journey-stage[data-stage]');
                let thoughts = '暂无想法';
                let actions = '暂无行为';

                if (stages[dataPointIndex]) {
                    const thoughtsItem = stages[dataPointIndex].querySelector('.thoughts-lane .item');
                    if (thoughtsItem) {
                        thoughts = thoughtsItem.textContent.trim() || '暂无想法';
                    }

                    const actionsItem = stages[dataPointIndex].querySelector('.actions-lane .item');
                    if (actionsItem) {
                        actions = actionsItem.textContent.trim() || '暂无行为';
                    }
                }

                let emoji = '😐';
                if (value >= 80) emoji = '😄';
                else if (value >= 60) emoji = '😊';
                else if (value >= 40) emoji = '😐';
                else if (value >= 20) emoji = '😟';
                else emoji = '😢';

                return `
                    <div style="padding: 16px; background: white; border-radius: 12px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 280px;">
                        <div style="font-weight: 700; color: #2d3748; margin-bottom: 8px; font-size: 14px;">${stage}</div>
                        <div style="color: #4a5568; margin-bottom: 8px; font-size: 13px;">情感值: ${value}</div>
                        <div style="font-size: 24px; text-align: center; margin: 8px 0;">${emoji}</div>
                        <div style="border-top: 1px solid #e2e8f0; padding-top: 8px; margin-top: 8px;">
                            <div style="font-weight: 600; color: #667eea; font-size: 12px; margin-bottom: 4px;">用户行为:</div>
                            <div style="color: #4a5568; font-size: 12px; line-height: 1.4; word-wrap: break-word; overflow-wrap: break-word; word-break: break-all; white-space: pre-wrap; max-width: 248px; margin-bottom: 8px;">${actions}</div>
                        </div>
                        <div style="border-top: 1px solid #e2e8f0; padding-top: 8px; margin-top: 8px;">
                            <div style="font-weight: 600; color: #667eea; font-size: 12px; margin-bottom: 4px;">想法 & 感受:</div>
                            <div style="color: #4a5568; font-size: 12px; line-height: 1.4; font-style: italic; word-wrap: break-word; overflow-wrap: break-word; word-break: break-all; white-space: pre-wrap; max-width: 248px;">${thoughts}</div>
                        </div>
                    </div>
                `;
            }
        },
        colors: ['#0d6efd']
    };

    chart = new ApexCharts(document.querySelector("#chart"), chartOptions);
    chart.render().then(() => {
        // 渲染完成后，移除所有可能的选择效果
        setTimeout(() => {
            const chartContainer = document.querySelector("#chart");
            if (chartContainer) {
                // 移除所有marker的选择状态
                const markers = chartContainer.querySelectorAll('.apexcharts-marker');
                markers.forEach(marker => {
                    marker.style.outline = 'none';
                    marker.style.boxShadow = 'none';
                    marker.removeAttribute('tabindex');

                    // 添加点击事件阻止选择
                    marker.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        // 移除任何可能的选择类
                        this.classList.remove('apexcharts-marker-selected');
                        this.style.outline = 'none';
                        this.style.boxShadow = 'none';
                    });
                });
            }
        }, 100);
    });

    // 创建新阶段
    function createNewStage() {
        // 获取所有现有阶段的data-stage值
        const existingStages = document.querySelectorAll('.journey-stage[data-stage]');
        const existingStageNumbers = Array.from(existingStages).map(stage =>
            parseInt(stage.getAttribute('data-stage'))
        ).filter(num => !isNaN(num));

        // 找到最大的阶段号，然后+1
        const maxStageNumber = existingStageNumbers.length > 0 ? Math.max(...existingStageNumbers) : 0;
        const newStageNumber = maxStageNumber + 1;
        const newStage = document.createElement('div');
        newStage.className = 'journey-stage';
        newStage.setAttribute('data-stage', newStageNumber);
        newStage.innerHTML = `
            <button class="delete-stage-btn">&times;</button>
            <div class="stage-header" contenteditable="true">阶段 ${newStageNumber}</div>
            <div class="sentiment-input-container">
                <label class="sentiment-label">情感值:</label>
                <input type="number" class="sentiment-input" min="0" max="100" value="50" data-stage="${newStageNumber}">
                <span class="sentiment-indicator">😐</span>
            </div>
            <div class="lane actions-lane">
                <div class="lane-title">用户行为</div>
                <div class="item" contenteditable="true">新行为</div>
            </div>
            <div class="lane thoughts-lane">
                <div class="lane-title">想法 & 感受</div>
                <div class="item" contenteditable="true">新想法</div>
            </div>
        `;

        // 添加删除按钮事件 - 添加二次确认
        const deleteBtn = newStage.querySelector('.delete-stage-btn');
        deleteBtn.addEventListener('click', () => {
            showCustomConfirm(() => {
                newStage.remove();
                updateSentimentChart();
                // 重新生成痛点按钮以保持正确的对应关系
                regenerateOpportunityButtons();
            });
        });

        // 添加情感值输入事件
        const sentimentInput = newStage.querySelector('.sentiment-input');
        sentimentInput.addEventListener('input', (e) => {
            updateSentimentIndicator(e.target);
            updateSentimentChart();
        });

        // 添加阶段标题变化事件
        const stageHeader = newStage.querySelector('.stage-header');
        stageHeader.addEventListener('input', updateSentimentChart);

        // 添加想法和感受内容变化事件
        const thoughtsItem = newStage.querySelector('.thoughts-lane .item');
        if (thoughtsItem) {
            thoughtsItem.addEventListener('input', updateSentimentChart);
            thoughtsItem.addEventListener('blur', updateSentimentChart);
        }

        stagesContainer.insertBefore(newStage, addStageBtn.parentElement);

        // 为新阶段添加拖拽功能
        if (window.addDragToStage) {
            window.addDragToStage(newStage);
        }

        updateSentimentChart();

        // 重新生成心情曲线下方的痛点/机会按钮
        updateChartOpportunityButtons();
    }

    // 卡片拖拽排序功能
    function initStageDragAndDrop() {
        const stagesContainer = document.querySelector('.journey-stages-container');
        let draggedStage = null;
        let placeholder = null;

        // 为所有现有阶段添加拖拽功能
        function addDragToStage(stage) {
            stage.draggable = true;

            // 只在点击空白区域时开始拖拽
            stage.addEventListener('mousedown', function(e) {
                // 如果点击的是可编辑内容或输入框，不启动拖拽
                if (e.target.contentEditable === 'true' ||
                    e.target.tagName === 'INPUT' ||
                    e.target.closest('.item') ||
                    e.target.closest('.sentiment-input-container')) {
                    stage.draggable = false;
                    return;
                }
                stage.draggable = true;
            });

            stage.addEventListener('dragstart', function(e) {
                draggedStage = this;
                this.style.opacity = '0.5';

                // 创建占位符
                placeholder = document.createElement('div');
                placeholder.className = 'stage-placeholder';
                placeholder.style.width = this.offsetWidth + 'px';
                placeholder.style.height = this.offsetHeight + 'px';
                placeholder.style.background = 'rgba(102, 126, 234, 0.2)';
                placeholder.style.border = '2px dashed #667eea';
                placeholder.style.borderRadius = '16px';
                placeholder.style.flexShrink = '0';
            });

            stage.addEventListener('dragend', function(e) {
                this.style.opacity = '1';
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedStage = null;
                placeholder = null;
            });
        }

        // 为容器添加拖拽事件
        stagesContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            if (!draggedStage || !placeholder) return;

            const afterElement = getDragAfterElement(stagesContainer, e.clientX);
            if (afterElement == null) {
                stagesContainer.appendChild(placeholder);
            } else {
                stagesContainer.insertBefore(placeholder, afterElement);
            }
        });

        stagesContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            if (!draggedStage || !placeholder) return;

            // 将拖拽的阶段插入到占位符位置
            stagesContainer.insertBefore(draggedStage, placeholder);
            stagesContainer.removeChild(placeholder);
        });

        // 获取拖拽后应该插入的位置
        function getDragAfterElement(container, x) {
            const draggableElements = [...container.querySelectorAll('.journey-stage:not(.stage-placeholder)')].filter(el => el !== draggedStage);

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = x - box.left - box.width / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // 为所有现有阶段添加拖拽功能
        document.querySelectorAll('.journey-stage').forEach(addDragToStage);

        // 返回函数以便为新创建的阶段添加拖拽功能
        return addDragToStage;
    }

    // 初始化现有阶段的删除按钮
    function initExistingStages() {
        document.querySelectorAll('.journey-stage[data-stage]').forEach(stage => {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-stage-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    stage.remove();
                    updateSentimentChart();
                    // 重新生成痛点按钮以保持正确的对应关系
                    regenerateOpportunityButtons();
                });
            });
            stage.prepend(deleteBtn);

            // 为现有的情感值输入添加事件监听
            const sentimentInput = stage.querySelector('.sentiment-input');
            if (sentimentInput) {
                sentimentInput.addEventListener('input', (e) => {
                    updateSentimentIndicator(e.target);
                    updateSentimentChart();
                });
                // 初始化情感指示器
                updateSentimentIndicator(sentimentInput);
            }

            // 为阶段标题添加事件监听
            const stageHeader = stage.querySelector('.stage-header');
            if (stageHeader) {
                stageHeader.addEventListener('input', updateSentimentChart);
            }

            // 为想法和感受内容添加事件监听
            const thoughtsItem = stage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }
        });
    }

    // 事件监听器
    addStageBtn.addEventListener('click', createNewStage);

    // 初始化用户模型区域的点击清空功能
    function initUserModelClearOnClick() {
        const editableContents = document.querySelectorAll('.user-model-content .editable-content');

        const defaultTexts = {
            'user-persona': '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
            'scenario': '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
            'goals': '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
        };

        editableContents.forEach(content => {
            content.addEventListener('click', function() {
                const p = this.querySelector('p');
                const parentClass = this.parentElement.className;
                let defaultText = '';

                if (parentClass.includes('user-persona')) {
                    defaultText = defaultTexts['user-persona'];
                } else if (parentClass.includes('scenario')) {
                    defaultText = defaultTexts['scenario'];
                } else if (parentClass.includes('goals')) {
                    defaultText = defaultTexts['goals'];
                }

                if (p && p.textContent.trim() === defaultText) {
                    p.textContent = '';
                    p.focus();
                }
            });

            // 当失去焦点且内容为空时，恢复提示文字
            content.addEventListener('blur', function() {
                const p = this.querySelector('p');
                if (p && p.textContent.trim() === '') {
                    const parentClass = this.parentElement.className;
                    if (parentClass.includes('user-persona')) {
                        p.textContent = defaultTexts['user-persona'];
                    } else if (parentClass.includes('scenario')) {
                        p.textContent = defaultTexts['scenario'];
                    } else if (parentClass.includes('goals')) {
                        p.textContent = defaultTexts['goals'];
                    }
                }
            });
        });
    }



    // 初始化
    initExistingStages();
    initUserModelClearOnClick();
    window.addDragToStage = initStageDragAndDrop();
    updateSentimentChart();

    // 初始化痛点/机会功能
    initOpportunities();

    // 初始化保存和加载功能
    initSaveLoadFunctionality();
});

// 心情曲线下方痛点/机会功能
function initOpportunities() {
    generateChartOpportunityButtons();
}

// 重新生成痛点按钮（保持现有卡片数据）
function regenerateOpportunityButtons() {
    // 保存现有的痛点卡片数据
    const existingOpportunities = {};
    const allCards = document.querySelectorAll('.opportunity-card');

    allCards.forEach(card => {
        const stageId = card.getAttribute('data-stage');
        const titleElement = card.querySelector('.opportunity-title span');
        const contentElement = card.querySelector('.opportunity-content');

        if (!existingOpportunities[stageId]) {
            existingOpportunities[stageId] = [];
        }

        if (titleElement && contentElement) {
            existingOpportunities[stageId].push({
                title: titleElement.textContent,
                content: contentElement.textContent
            });
        }
    });

    // 移除现有按钮
    const existingButtons = document.getElementById('chartOpportunityButtons');
    if (existingButtons) {
        existingButtons.remove();
    }

    // 重新生成按钮
    generateChartOpportunityButtons();

    // 恢复保存的痛点卡片数据
    Object.keys(existingOpportunities).forEach(stageId => {
        const opportunities = existingOpportunities[stageId];
        opportunities.forEach(opp => {
            // 检查对应的阶段是否还存在
            const stageExists = document.querySelector(`.journey-stage[data-stage="${stageId}"]`);
            if (stageExists) {
                restoreOpportunityCard(stageId, opp.title, opp.content);
            }
        });
    });
}

function generateChartOpportunityButtons() {
    const chartOpportunitiesSection = document.getElementById('chartOpportunitiesSection');
    if (!chartOpportunitiesSection) return;

    // 获取所有阶段
    const stages = document.querySelectorAll('.journey-stage[data-stage]');
    const stageCount = stages.length;

    // 创建按钮容器
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'chart-opportunities-buttons';
    buttonsContainer.id = 'chartOpportunityButtons';

    stages.forEach((stage, index) => {
        const stageId = stage.getAttribute('data-stage');
        const stageHeader = stage.querySelector('.stage-header');
        const stageName = stageHeader ? stageHeader.textContent.trim() : `阶段 ${stageId}`;

        // 创建每个阶段的列
        const stageColumn = document.createElement('div');
        stageColumn.className = 'chart-stage-column';
        stageColumn.setAttribute('data-stage', stageId);

        // 根据阶段数量和位置调整对齐方式
        if (stageCount <= 7) {
            if (index === 0) {
                stageColumn.style.alignItems = 'flex-start';
            } else if (index === stageCount - 1) {
                stageColumn.style.alignItems = 'flex-end';
            } else {
                stageColumn.style.alignItems = 'center';
            }
        } else {
            stageColumn.style.alignItems = 'center';
            // 对于超过7个阶段，启用水平滚动
            buttonsContainer.style.justifyContent = 'flex-start';
        }

        // 机会卡片容器
        const opportunitiesContainer = document.createElement('div');
        opportunitiesContainer.className = 'chart-stage-opportunities';
        opportunitiesContainer.setAttribute('data-stage', stageId);

        // 按钮
        const button = document.createElement('button');
        button.className = 'chart-opportunity-btn';
        button.setAttribute('data-stage', stageId);
        button.innerHTML = `
            <span class="btn-text">${stageName}</span>
        `;

        // 添加点击事件
        button.addEventListener('click', function() {
            // 从按钮的data-stage属性获取正确的stageId
            const buttonStageId = this.getAttribute('data-stage');
            const buttonStageElement = document.querySelector(`.journey-stage[data-stage="${buttonStageId}"]`);
            const buttonStageHeader = buttonStageElement ? buttonStageElement.querySelector('.stage-header') : null;
            const buttonStageName = buttonStageHeader ? buttonStageHeader.textContent.trim() : `阶段 ${buttonStageId}`;

            // 获取对应的容器
            const targetContainer = document.querySelector(`.chart-stage-opportunities[data-stage="${buttonStageId}"]`);
            if (targetContainer) {
                // 重新计算当前阶段的卡片数量，确保编号连续
                const currentCards = targetContainer.querySelectorAll('.opportunity-card');
                const opportunityCounter = currentCards.length + 1;
                addChartOpportunityCard(buttonStageId, opportunityCounter, buttonStageName);
            }
        });

        stageColumn.appendChild(opportunitiesContainer);
        stageColumn.appendChild(button);
        buttonsContainer.appendChild(stageColumn);
    });

    chartOpportunitiesSection.appendChild(buttonsContainer);
}



function addChartOpportunityCard(stageId, counter, stageName) {
    const container = document.querySelector(`.chart-stage-opportunities[data-stage="${stageId}"]`);
    if (!container) return;

    const opportunityCard = document.createElement('div');
    opportunityCard.className = 'opportunity-card';
    opportunityCard.setAttribute('data-stage', stageId);
    opportunityCard.innerHTML = `
        <div class="opportunity-header">
            <div class="opportunity-title">
                <span>${stageName}</span>
            </div>
            <button class="delete-opportunity-btn" onclick="deleteOpportunityCard(this)">×</button>
        </div>
        <div class="opportunity-content" contenteditable="true" placeholder="在这里描述发现的痛点或改进机会...">在这里描述发现的痛点或改进机会...</div>
    `;

    // 添加到对应阶段的容器（新卡片添加到最上面）
    container.insertBefore(opportunityCard, container.firstChild);

    // 为新卡片添加事件监听
    const contentDiv = opportunityCard.querySelector('.opportunity-content');

    // 点击时清空默认文本
    contentDiv.addEventListener('focus', function() {
        if (this.textContent === '在这里描述发现的痛点或改进机会...') {
            this.textContent = '';
        }
    });

    // 失去焦点时如果为空则恢复默认文本
    contentDiv.addEventListener('blur', function() {
        if (this.textContent.trim() === '') {
            this.textContent = '在这里描述发现的痛点或改进机会...';
        }
    });

    // 添加点击事件，点击时置于顶层（但不包括编辑区域）
    opportunityCard.addEventListener('click', function(e) {
        // 如果点击的是可编辑内容区域或删除按钮，不触发置顶
        if (e.target.contentEditable === 'true' ||
            e.target.classList.contains('delete-opportunity-btn') ||
            e.target.closest('.opportunity-content')) {
            return;
        }
        console.log('Card clicked:', this); // 调试日志
        e.stopPropagation(); // 防止事件冒泡
        bringCardToTop(this);
    });

    // 自动聚焦到新卡片
    setTimeout(() => {
        contentDiv.focus();
    }, 100);
}

function updateChartOpportunityButtons() {
    // 保存现有的痛点卡片数据
    const existingOpportunities = {};
    const existingContainers = document.querySelectorAll('.chart-stage-opportunities');

    existingContainers.forEach(container => {
        const stageId = container.getAttribute('data-stage');
        const cards = container.querySelectorAll('.opportunity-card');
        existingOpportunities[stageId] = [];

        cards.forEach(card => {
            const titleElement = card.querySelector('.opportunity-title span:last-child');
            const contentElement = card.querySelector('.opportunity-content');
            if (titleElement && contentElement) {
                existingOpportunities[stageId].push({
                    title: titleElement.textContent,
                    content: contentElement.textContent
                });
            }
        });
    });

    // 移除现有按钮
    const existingButtons = document.getElementById('chartOpportunityButtons');
    if (existingButtons) {
        existingButtons.remove();
    }

    // 重新生成按钮
    generateChartOpportunityButtons();

    // 恢复保存的痛点卡片数据
    Object.keys(existingOpportunities).forEach(stageId => {
        const opportunities = existingOpportunities[stageId];
        opportunities.forEach(opportunity => {
            restoreOpportunityCard(stageId, opportunity.title, opportunity.content);
        });
    });
}

// 恢复痛点/机会卡片
function restoreOpportunityCard(stageId, title, content) {
    const container = document.querySelector(`.chart-stage-opportunities[data-stage="${stageId}"]`);
    if (!container) return;

    const opportunityCard = document.createElement('div');
    opportunityCard.className = 'opportunity-card';
    opportunityCard.setAttribute('data-stage', stageId);
    opportunityCard.innerHTML = `
        <div class="opportunity-header">
            <div class="opportunity-title">
                <span>${title}</span>
            </div>
            <button class="delete-opportunity-btn" onclick="deleteOpportunityCard(this)">×</button>
        </div>
        <div class="opportunity-content" contenteditable="true" placeholder="在这里描述发现的痛点或改进机会...">${content}</div>
    `;

    // 添加到对应阶段的容器
    container.insertBefore(opportunityCard, container.firstChild);

    // 为恢复的卡片添加事件监听
    const contentDiv = opportunityCard.querySelector('.opportunity-content');

    // 点击时清空默认文本
    contentDiv.addEventListener('focus', function() {
        if (this.textContent === '在这里描述发现的痛点或改进机会...') {
            this.textContent = '';
        }
    });

    // 失去焦点时如果为空则恢复默认文本
    contentDiv.addEventListener('blur', function() {
        if (this.textContent.trim() === '') {
            this.textContent = '在这里描述发现的痛点或改进机会...';
        }
    });

    // 添加点击事件，点击时置于顶层（但不包括编辑区域）
    opportunityCard.addEventListener('click', function(e) {
        // 如果点击的是可编辑内容区域或删除按钮，不触发置顶
        if (e.target.contentEditable === 'true' ||
            e.target.classList.contains('delete-opportunity-btn') ||
            e.target.closest('.opportunity-content')) {
            return;
        }
        console.log('Restored card clicked:', this); // 调试日志
        e.stopPropagation(); // 防止事件冒泡
        bringCardToTop(this);
    });
}

// 将卡片置于顶层
function bringCardToTop(clickedCard) {
    // 移除所有卡片的顶层样式并清除位置
    const allCards = document.querySelectorAll('.opportunity-card');
    allCards.forEach(card => {
        if (card !== clickedCard) {
            card.classList.remove('top-layer');
            // 清除保存的位置信息
            card.style.left = '';
            card.style.top = '';
        }
    });

    // 移除所有阶段容器的顶层样式
    const allStages = document.querySelectorAll('.journey-stage');
    allStages.forEach(stage => {
        stage.classList.remove('stage-top-layer');
    });

    // 切换点击卡片的置顶状态
    if (clickedCard.classList.contains('top-layer')) {
        // 如果已经是置顶状态，则取消置顶
        clickedCard.classList.remove('top-layer');
        clickedCard.style.left = '';
        clickedCard.style.top = '';
    } else {
        // 直接添加置顶样式，使用相对定位和transform
        clickedCard.classList.add('top-layer');

        // 为包含该卡片的阶段容器添加顶层样式
        const parentStage = clickedCard.closest('.journey-stage');
        if (parentStage) {
            parentStage.classList.add('stage-top-layer');
        }
    }
}

// 删除痛点/机会卡片
function deleteOpportunityCard(deleteBtn) {
    const card = deleteBtn.closest('.opportunity-card');

    // 使用自定义确认对话框
    showCustomConfirm(() => {
        // 添加删除动画
        card.style.transform = 'translateX(-100%)';
        card.style.opacity = '0';

        setTimeout(() => {
            card.remove();
        }, 300);
    });
}

// 设置全局点击处理，点击其他地方时取消所有卡片的置顶状态
function setupGlobalClickHandler() {
    document.addEventListener('click', function(e) {
        // 如果点击的不是卡片，则取消所有置顶状态
        if (!e.target.closest('.opportunity-card')) {
            const allCards = document.querySelectorAll('.opportunity-card');
            allCards.forEach(card => {
                card.classList.remove('top-layer');
                card.style.left = '';
                card.style.top = '';
            });

            // 移除所有阶段容器的顶层样式
            const allStages = document.querySelectorAll('.journey-stage');
            allStages.forEach(stage => {
                stage.classList.remove('stage-top-layer');
            });
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    setupGlobalClickHandler();
});

// 保存和加载功能
function initSaveLoadFunctionality() {
    const saveBtn = document.getElementById('saveBtn');
    const loadBtn = document.getElementById('loadBtn');
    const fileInput = document.getElementById('fileInput');

    // 保存功能
    saveBtn.addEventListener('click', saveCurrentData);

    // 加载功能
    loadBtn.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', loadDataFromFile);

    // 自动保存到localStorage
    setInterval(autoSave, 30000); // 每30秒自动保存一次

    // 页面加载时不自动恢复数据，保持初始状态
    // loadAutoSavedData(); // 已禁用自动加载

    // 清除localStorage中的自动保存数据，确保每次刷新都是初始状态
    localStorage.removeItem('userJourneyMapData');
}

// 收集当前页面所有数据
function collectCurrentData() {
    const data = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        userModel: {},
        journeyStages: [],
        opportunities: []
    };

    // 收集用户模型数据
    const userPersona = document.querySelector('.user-persona .editable-content p');
    const scenario = document.querySelector('.scenario .editable-content p');
    const goals = document.querySelector('.goals .editable-content p');

    data.userModel = {
        persona: userPersona ? userPersona.textContent.trim() : '',
        scenario: scenario ? scenario.textContent.trim() : '',
        goals: goals ? goals.textContent.trim() : ''
    };

    // 收集用户体验阶段数据
    const stages = document.querySelectorAll('.journey-stage[data-stage]');
    stages.forEach(stage => {
        const stageData = {
            stageNumber: stage.getAttribute('data-stage'),
            header: stage.querySelector('.stage-header').textContent.trim(),
            sentimentValue: parseInt(stage.querySelector('.sentiment-input').value) || 0,
            actions: stage.querySelector('.actions-lane .item').textContent.trim(),
            thoughts: stage.querySelector('.thoughts-lane .item').textContent.trim()
        };
        data.journeyStages.push(stageData);
    });

    // 收集痛点/机会数据
    const opportunityCards = document.querySelectorAll('.opportunity-card');
    opportunityCards.forEach((card, index) => {
        // 获取标题，优先从span:last-child获取，如果没有则直接获取textContent
        const titleElement = card.querySelector('.opportunity-title span:last-child') ||
                           card.querySelector('.opportunity-title');
        const contentElement = card.querySelector('.opportunity-content');

        if (titleElement && contentElement) {
            const opportunityData = {
                id: card.getAttribute('data-opportunity-id') || `opp-${Date.now()}-${index}`,
                stageNumber: card.getAttribute('data-stage'),
                type: card.classList.contains('pain-point') ? 'pain-point' : 'opportunity',
                title: titleElement.textContent.trim(),
                content: contentElement.textContent.trim(),
                position: {
                    left: card.style.left,
                    top: card.style.top
                },
                isTopLayer: card.classList.contains('top-layer')
            };
            data.opportunities.push(opportunityData);
        }
    });

    return data;
}

// 保存数据到文件
function saveCurrentData() {
    try {
        const data = collectCurrentData();
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `用户体验地图_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 同时保存到localStorage
        localStorage.setItem('userJourneyMapData', jsonString);

        showNotification('数据保存成功！', 'success');
    } catch (error) {
        console.error('保存数据时出错:', error);
        showNotification('保存失败，请重试', 'error');
    }
}

// 从文件加载数据
function loadDataFromFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('开始加载文件:', file.name);

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            console.log('文件内容:', e.target.result);
            const data = JSON.parse(e.target.result);
            console.log('解析后的数据:', data);

            loadDataToPage(data);
            showNotification('数据加载成功！', 'success');
        } catch (error) {
            console.error('加载数据时出错:', error);
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
            showNotification(`加载失败: ${error.message}`, 'error');
        }
    };

    reader.onerror = function(e) {
        console.error('文件读取错误:', e);
        showNotification('文件读取失败', 'error');
    };

    reader.readAsText(file);

    // 清空文件输入，允许重复选择同一文件
    event.target.value = '';
}

// 将数据加载到页面
function loadDataToPage(data) {
    console.log('开始加载数据到页面:', data);

    if (!data || typeof data !== 'object') {
        throw new Error('无效的数据格式');
    }

    try {

    // 加载用户模型数据
    if (data.userModel) {
        const userPersona = document.querySelector('.user-persona .editable-content p');
        const scenario = document.querySelector('.scenario .editable-content p');
        const goals = document.querySelector('.goals .editable-content p');

        if (userPersona && data.userModel.persona) {
            userPersona.textContent = data.userModel.persona;
        }
        if (scenario && data.userModel.scenario) {
            scenario.textContent = data.userModel.scenario;
        }
        if (goals && data.userModel.goals) {
            goals.textContent = data.userModel.goals;
        }
    }

    // 清除现有阶段（除了添加按钮）
    const existingStages = document.querySelectorAll('.journey-stage[data-stage]');
    existingStages.forEach(stage => stage.remove());

    // 加载用户体验阶段数据
    if (data.journeyStages && Array.isArray(data.journeyStages)) {
        const stagesContainer = document.querySelector('.journey-stages-container');
        const addStageBtn = document.querySelector('.add-stage-button-container');

        data.journeyStages.forEach((stageData, index) => {
            const newStage = document.createElement('div');
            newStage.className = 'journey-stage';
            newStage.setAttribute('data-stage', stageData.stageNumber || (index + 1));

            newStage.innerHTML = `
                <button class="delete-stage-btn">&times;</button>
                <div class="stage-header" contenteditable="true">${stageData.header || `阶段 ${index + 1}`}</div>
                <div class="sentiment-input-container">
                    <label class="sentiment-label">情感值:</label>
                    <input type="number" class="sentiment-input" min="0" max="100" value="${stageData.sentimentValue || 50}" data-stage="${stageData.stageNumber || (index + 1)}">
                    <span class="sentiment-indicator">😐</span>
                </div>
                <div class="lane actions-lane">
                    <div class="lane-title">用户行为</div>
                    <div class="item" contenteditable="true">${stageData.actions || '新行为'}</div>
                </div>
                <div class="lane thoughts-lane">
                    <div class="lane-title">想法 & 感受</div>
                    <div class="item" contenteditable="true">${stageData.thoughts || '新想法'}</div>
                </div>
            `;

            // 添加事件监听器
            const deleteBtn = newStage.querySelector('.delete-stage-btn');
            deleteBtn.addEventListener('click', () => {
                showCustomConfirm(() => {
                    newStage.remove();
                    updateSentimentChart();
                    // 重新生成痛点按钮以保持正确的对应关系
                    regenerateOpportunityButtons();
                });
            });

            const sentimentInput = newStage.querySelector('.sentiment-input');
            sentimentInput.addEventListener('input', (e) => {
                updateSentimentIndicator(e.target);
                updateSentimentChart();
            });

            const stageHeader = newStage.querySelector('.stage-header');
            stageHeader.addEventListener('input', updateSentimentChart);

            const thoughtsItem = newStage.querySelector('.thoughts-lane .item');
            if (thoughtsItem) {
                thoughtsItem.addEventListener('input', updateSentimentChart);
                thoughtsItem.addEventListener('blur', updateSentimentChart);
            }

            // 更新情感指示器
            updateSentimentIndicator(sentimentInput);

            // 添加拖拽功能
            if (window.addDragToStage) {
                window.addDragToStage(newStage);
            }

            stagesContainer.insertBefore(newStage, addStageBtn);
        });
    }

    // 更新心情曲线
    updateSentimentChart();

    // 重新生成痛点/机会按钮
    updateChartOpportunityButtons();

    // 等待DOM更新后再加载痛点/机会数据
    setTimeout(() => {
        if (data.opportunities && Array.isArray(data.opportunities)) {
            console.log('开始加载机会卡片数据:', data.opportunities);

            // 清除现有的机会卡片
            const existingCards = document.querySelectorAll('.opportunity-card');
            existingCards.forEach(card => card.remove());

            // 重新创建机会卡片
            data.opportunities.forEach(oppData => {
                console.log('恢复机会卡片:', oppData);
                if (oppData.stageNumber && oppData.title && oppData.content) {
                    const container = document.querySelector(`.chart-stage-opportunities[data-stage="${oppData.stageNumber}"]`);
                    if (container) {
                        restoreOpportunityCard(oppData.stageNumber, oppData.title, oppData.content);
                    } else {
                        console.warn(`找不到阶段 ${oppData.stageNumber} 的容器`);
                    }
                }
            });
        }
    }, 100);

    console.log('数据加载完成');

    } catch (error) {
        console.error('loadDataToPage 执行错误:', error);
        throw error; // 重新抛出错误以便上层处理
    }
}



// 自动保存功能
function autoSave() {
    try {
        const data = collectCurrentData();
        localStorage.setItem('userJourneyMapData', JSON.stringify(data));
        console.log('自动保存完成');
    } catch (error) {
        console.error('自动保存失败:', error);
    }
}

// 加载自动保存的数据
function loadAutoSavedData() {
    try {
        const savedData = localStorage.getItem('userJourneyMapData');
        if (savedData) {
            const data = JSON.parse(savedData);
            // 只在页面是默认状态时才自动加载
            if (isPageInDefaultState()) {
                loadDataToPage(data);
                showNotification('已恢复上次保存的数据', 'info');
            }
        }
    } catch (error) {
        console.error('加载自动保存数据失败:', error);
    }
}

// 检查页面是否处于默认状态
function isPageInDefaultState() {
    const stages = document.querySelectorAll('.journey-stage[data-stage]');
    if (stages.length !== 2) return false;

    const userPersona = document.querySelector('.user-persona .editable-content p');
    const scenario = document.querySelector('.scenario .editable-content p');
    const goals = document.querySelector('.goals .editable-content p');

    const defaultTexts = [
        '描述目标用户的基本信息，包括年龄、职业、技能水平、使用习惯等关键特征...',
        '描述用户使用产品的具体场景，包括时间、地点、环境、触发因素等背景信息...',
        '明确用户想要达成的目标，包括功能性需求、情感性需求和期望的结果...'
    ];

    return userPersona && defaultTexts.includes(userPersona.textContent.trim()) &&
           scenario && defaultTexts.includes(scenario.textContent.trim()) &&
           goals && defaultTexts.includes(goals.textContent.trim());
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        padding: 12px 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 1001;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    // 动画显示
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 3秒后自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}
